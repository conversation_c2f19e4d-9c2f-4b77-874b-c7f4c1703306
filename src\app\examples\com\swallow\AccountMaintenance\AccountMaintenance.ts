import { GridRow } from './../../../../modules/swt-toolbox/com/swallow/containers/swt-grid.component';
import { SwtCheckBox } from './../../../../modules/swt-toolbox/com/swallow/controls/swt-checkbox.component';
import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { Alert, CommonService, ContextMenuItem, ExternalInterface, HBox, HTTPComms, JSONReader, JSONViewer, Logger, ScreenVersion, StringUtils, SwtAlert, SwtButton, SwtCanvas, SwtComboBox, SwtCommonGrid, SwtLabel, SwtLoadingImage, SwtModule, SwtPopUpManager, SwtTextInput, SwtToolBoxModule, Swt<PERSON><PERSON>, Timer, VBox } from './../../../../modules/swt-toolbox';
declare var instanceElement: any;
@Component({
  selector: 'app-account-maintenance',
  templateUrl: './AccountMaintenance.html',
  styleUrls: ['./AccountMaintenance.css'],
  encapsulation: ViewEncapsulation.None
})
export class AccountMaintenance  extends SwtModule implements OnInit {
 
  public jsonReader: JSONReader = new JSONReader();
  public lastReceivedJSON;
  public prevReceivedJSON;
  private  currencyCalculationGrid: SwtCommonGrid;
  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private deleteData = new HTTPComms(this.commonService);
  private baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = null;
  private actionPath: string = null;
  private requestParams = [];
  private invalidComms: string = null;
  private closeWindow = false;
  private entityId: string;
  private entityCode: String;
  private currencyCode: string;
  private accountId: string;
  private accountName: string;
  private linkedAccount: String ;
  private refreshRate = 120;
  private ccyIsEmpty: string = null;
  private swtAlert: SwtAlert;
  private errorLocation = 0;
  private moduleId ="Predict";
  private menuAccessId  = 0;
  private menuEntityCurrGrpAccess = 0;
  private menuAccess  = "";
  private status  = "";
  private autoRefresh: Timer = null;
  // Variable that holds the version number for this mxml
  private versionNumber = "1";
  public screenVersion = new ScreenVersion(this.commonService);
  private screenName = "Account Maintenance";
  private versionDate = "04/11/2023";
  private logger: Logger;
  private ccyEntityAccess: string = null;
  // private totalsGrid: SwtCommonGrid;
  // private  buttonBarHideFlag:boolean = false;
  private methodName = "";
  private selectedAccountId = null;
  private calledFromILMCcy = "false";
  private isLinkedAccount = "false";
/**
   * Popup Objects
   **/
 private showJsonPopup = null;
 @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
 @ViewChild('gridCanvas') gridCanvas: SwtCanvas;
  //  @ViewChild('quickFilterBox') quickFilterBox: VBox;
 
 /*********Combobox*********/
 @ViewChild('entityCombo') entityCombo: SwtComboBox;
 @ViewChild('ccyCombo') ccyCombo: SwtComboBox;

 /*********SwtLabel*********/
 @ViewChild('currencyLabel') currencyLabel: SwtLabel;
 @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  //  @ViewChild('FiltersLabel') FiltersLabel: SwtLabel;
 
 @ViewChild('entityLabel') entityLabel: SwtLabel;
 @ViewChild('selectedEntity') selectedEntity: SwtLabel

  //  @ViewChild('totalsContainer') totalsContainer: SwtCanvas;
 @ViewChild("addButton") addButton: SwtButton;
 @ViewChild("changeButton") changeButton: SwtButton;
 @ViewChild("deleteButton") deleteButton: SwtButton;
 @ViewChild("viewButton") viewButton: SwtButton;
 @ViewChild("closeButton") closeButton: SwtButton;
 @ViewChild("okbutton") okbutton: SwtButton;



 @ViewChild("currrencyCodeTextInput") currrencyCodeTextInput: SwtTextInput;
 @ViewChild("entityIdTextInput") entityIdTextInput: SwtTextInput;
 
  //  @ViewChild('imgShowHideButtonBar') imgShowHideButtonBar: SwtButton;
 @ViewChild('entityComboHBox') entityComboHBox: HBox;

 @ViewChild('ccyComboHBox') ccyComboHBox: HBox;
 

  @ViewChild('openBox') openBox: SwtCheckBox;
  @ViewChild('blockedBox') blockedBox: SwtCheckBox;
  @ViewChild('closedBox') closedBox: SwtCheckBox;
  @ViewChild('emptyBox') emptyBox: SwtCheckBox;

  @ViewChild('openLabel') openLabel: SwtLabel;
  @ViewChild('blockedLabel') blockedLabel: SwtLabel;
  @ViewChild('closedLabel') closedLabel: SwtLabel;

  @ViewChild('emptyLabel') emptyLabel: SwtLabel;


  @ViewChild('statusHbox') statusHbox: GridRow;








// private accountEntityName = null;
// private accountCurrencyName = null;

  constructor(private commonService: CommonService, private element: ElementRef) {
  super(element, commonService);
  this.logger = new Logger('Account Maintenance Screen', this.commonService.httpclient);
  this.swtAlert = new SwtAlert(commonService);
  window["Main"] = this;
}


  ngOnInit() {
    instanceElement = this;
    try {
      
    
      // this.totalsGrid = <SwtCommonGrid>this.totalsContainer.addChild(SwtCommonGrid);
    this.currencyCalculationGrid = this.gridCanvas.addChild(SwtCommonGrid) as SwtCommonGrid;
    this.currencyCalculationGrid.lockedColumnCount = 1;
      this.entityLabel.text = SwtUtil.getPredictMessage('entity.id', null);
      this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntity', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('matchQuality.currencyCode', null);
      this.ccyCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectCurrencyCode', null);
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    this.menuEntityCurrGrpAccess = ExternalInterface.call('eval', 'menuEntityCurrGrpAccess');
    
      this.methodName = ExternalInterface.call("eval", "methodNameValue");

    // this.methodName = "";
    // this.menuAccessId = 0;
    // this.menuEntityCurrGrpAccess = 0;

    this.addButton.toolTip = SwtUtil.getPredictMessage('tooltip.addNewAccount', null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage('tooltip.changeSelAc', null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage('tooltip.viewSelAc', null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage('tooltip.deleteSelAc', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('button.close', null);
      this.isLinkedAccount = ExternalInterface.call("eval", "linkedAccount");
      // this.totalsGrid.editable=true;

    this.currencyCalculationGrid.onRowClick = (event) => {
      this.cellClickEventHandler(event);
    };
      // this.methodName = "calledFromILMCcy";
      console.log("🚀 ~ file: AccountMaintenance.ts:173 ~ ngOnInit ~ this.methodName:", this.methodName)
      if ("subAccounts" == this.methodName) {
        this.showHideEntityCcyTextInuts(true);
        this.statusHbox.visible = false;
        this.statusHbox.includeInLayout = false;
        // this.selectedCcy.text = this.ccyCombo.selectedItem.value;
        // this.selectedEntity.text = this.entityCombo.selectedItem.value;

        this.selectedCcy.text = ExternalInterface.call("eval", "accountEntityName");
        this.selectedEntity.text = ExternalInterface.call("eval", "accountCurrencyName");
        this.viewButton.enabled = false;

        this.okbutton.includeInLayout = false;
        this.okbutton.visible = false;

        this.addButton.includeInLayout = false;
        this.addButton.visible = false;

        this.changeButton.includeInLayout = false;
        this.changeButton.visible = false;


        this.deleteButton.includeInLayout = false;
        this.deleteButton.visible = false;
    
      this.showHideEntityCcyTextInuts(true);
      try {
          this.entityIdTextInput.text = ExternalInterface.call("eval", "selectedEntity");
          this.currrencyCodeTextInput.text = ExternalInterface.call("eval", "selectedCurrency");
          this.selectedCcy.text = ExternalInterface.call("eval", "accountCurrencyName");
          this.selectedEntity.text = ExternalInterface.call("eval", "accountEntityName");

    } catch (error) {
          console.log("error", error);

          
    }
      } else {
        this.calledFromILMCcy = ExternalInterface.call("eval", "calledFromILMCcy");



        try {
          this.entityIdTextInput.text = ExternalInterface.call("eval", "selectedEntity");
          this.currrencyCodeTextInput.text = ExternalInterface.call("eval", "selectedCurrency");
          this.selectedCcy.text = ExternalInterface.call("eval", "accountCurrencyName");
          this.selectedEntity.text = ExternalInterface.call("eval", "accountEntityName");
        
      } catch (error) {
          console.log("error", error);
        
          
      }
        
        if (this.calledFromILMCcy == "true") {
        this.addButton.includeInLayout = false;
        this.addButton.visible = false;

        this.changeButton.includeInLayout = false;
        this.changeButton.visible = false;

        this.viewButton.includeInLayout = false;
        this.viewButton.visible = false;

        this.deleteButton.includeInLayout = false;
        this.deleteButton.visible = false;
          this.showHideEntityCcyTextInuts(true);
          // this.closeButton.includeInLayout = false;
          // this.closeButton.visible = false;

        } else {
        this.okbutton.includeInLayout = false;
        this.okbutton.visible = false;
        this.showHideEntityCcyTextInuts(false);
        this.addButton.enabled = (this.menuAccessId == 0);
      }
      this.currencyCalculationGrid.allowMultipleSelection = true;
    }
  } catch (error) {
      console.log("error", error);
      
  }

  }

  showHideEntityCcyTextInuts(show) {
    
    if (show) {

      this.entityComboHBox.visible = false;
      this.entityComboHBox.includeInLayout = false;
      this.ccyComboHBox.visible = false;
      this.ccyComboHBox.includeInLayout = false;

    } else {
      this.entityIdTextInput.visible = false;
      this.entityIdTextInput.includeInLayout = false;
      this.currrencyCodeTextInput.visible = false;
      this.currrencyCodeTextInput.includeInLayout = false;

    }



  }

  /**
   * Upon completion of loading into the flash player this method is called
   **/
   onLoad(): void {
    this.loadingImage.setVisible(false);

    if (this.menuAccess) {
      if (this.menuAccess !== "") {
        this.menuAccessId = Number(this.menuAccess);
      }
    }
    // set version number
    this.initializeMenus();

    // result event
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);


    this.deleteData.cbFault = this.inputDataFault.bind(this);
    this.deleteData.encodeURL = false;
    this.deleteData.cbStart = this.startOfComms.bind(this);
    this.deleteData.cbStop = this.endOfComms.bind(this);





    // action url	
    this.actionPath = "acctMaintenance.do?method=";

    this.requestParams = [];

    if (this.methodName != "subAccounts") {//&& this.methodName != "calledFromILMCcy"){
      // Then declare the action method:					
      this.actionMethod = "displayListByEntityAngular";

      if (this.calledFromILMCcy == "true") {
        this.entityId = ExternalInterface.call("eval", "selectedEntity");
        this.currencyCode = ExternalInterface.call("eval", "selectedCurrency");
      }
    } else {
      if (this.methodName == "subAccounts") {

        this.entityId = ExternalInterface.call("eval", "selectedEntity");
        this.currencyCode = ExternalInterface.call("eval", "selectedCurrency");
        this.selectedAccountId = ExternalInterface.call("eval", "accountId");
        this.requestParams["selectedAccountId"] = this.selectedAccountId;
        this.requestParams["linkedAccount"] = this.isLinkedAccount;

    // Then declare the action method:					
        this.actionMethod = "subAccountsAngular";
      }

    }


    this.requestParams["entityId"] = this.entityId;
    this.requestParams["selectedCurrencyCode"] = this.currencyCode;

    let checkedStatus = '';
    checkedStatus += this.openBox.selected ? 'Y' : 'N';
    checkedStatus += this.blockedBox.selected ? 'Y' : 'N';
    checkedStatus += this.closedBox.selected ? 'Y' : 'N';
    checkedStatus += this.emptyBox.selected ? 'Y' : 'N';


    this.requestParams["checkedStatus"] = checkedStatus;


    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    // Make initial request
    this.inputData.send(this.requestParams);



  }


/**
   * The function initializes the menus in the right click event on the Entity Monitor screen.
   * The links are redirected to their respective pages.
   */
  private initializeMenus(): void {
  this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.versionDate);
  let addMenuItem = new ContextMenuItem("Show JSON");
  // add the listener to addMenuItem
  addMenuItem.MenuItemSelect = this.showJSONSelect.bind(this);
  this.screenVersion.svContextMenu.customItems.push(addMenuItem);
    this.contextMenu = this.screenVersion.svContextMenu;
}
/** This function is used to display the XML for the monitor selected in the monitor combo
   */
 showJSONSelect(event): void {

  this.showJsonPopup = SwtPopUpManager.createPopUp(this,
    JSONViewer,
    {
      jsonData: this.lastReceivedJSON,
    });
  this.showJsonPopup.width = "700";
  this.showJsonPopup.title = "Last Received JSON";
  this.showJsonPopup.height = "500";
  this.showJsonPopup.enableResize = false;
  this.showJsonPopup.showControls = true;
  this.showJsonPopup.display();
}



/**
   * This method is called by the HTTPComms when result event occurs.
   * @param event:ResultEvent
   * */
 inputDataResult(event): void {
  // get the received xml
    this.lastReceivedJSON = event;
  this.jsonReader.setInputJSON(this.lastReceivedJSON);
  if (this.jsonReader.getRequestReplyStatus()) {
    if ((this.lastReceivedJSON != this.prevReceivedJSON)) {
      if (!this.jsonReader.isDataBuilding()) {
        //disable all buttons
        this.disableOrEnableButtons(false);
          if (this.methodName != "subAccounts") {
            this.ccyCombo.setComboData(this.jsonReader.getSelects(), false);
            this.entityCombo.setComboData(this.jsonReader.getSelects(), false);
            this.selectedCcy.text = this.ccyCombo.selectedItem.value;
            this.selectedEntity.text = this.entityCombo.selectedItem.value;
        }

        /*****grid display***********/
          const obj = { columns: this.jsonReader.getColumnData() };
          
        
          this.createColumnFilter(obj);


          this.currencyCalculationGrid.doubleClickEnabled = true;
        this.currencyCalculationGrid.CustomGrid(obj);
          this.currencyCalculationGrid.refreshFilters();
        if (this.jsonReader.getGridData()) {
          if (this.jsonReader.getGridData().size > 0) {
              this.currencyCalculationGrid.gridData = this.jsonReader.getGridData();
              this.currencyCalculationGrid.setRowSize = this.jsonReader.getRowSize();
              this.currencyCalculationGrid.selectedIndex = -1;

          } else {
            this.currencyCalculationGrid.dataProvider = null;
            this.currencyCalculationGrid.selectedIndex = -1;
          }
        } else {
          this.currencyCalculationGrid.dataProvider = null;
          this.currencyCalculationGrid.selectedIndex = -1;
        }

          this.lastSelectedFilterCheckboxes = this.getCurrentCheckboxesState();

          if (this.calledFromILMCcy == "true") {
            let tempIndex = this.currencyCalculationGrid.gridData.findIndex(x => x.accountId == this.selectedAccountId);
            if (tempIndex >= 0) {
              this.currencyCalculationGrid.selectedIndex = tempIndex;
            }

          }

        /**AutoRefresh******************/
        if (this.autoRefresh == null) {
          this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
          this.autoRefresh.addEventListener("timer", this.refreshdetails.bind(this));
        } else {
          this.autoRefresh.delay(this.refreshRate * 1000);
        }
      }
        this.prevReceivedJSON = this.lastReceivedJSON;
    }
  } else {
    if (this.lastReceivedJSON.hasOwnProperty("requestthis.reply")) {
      if (this.lastReceivedJSON.requestthis.reply.closewindow == "true") {
          this.closeWindow = true;
      }
    }
    this.swtAlert.warning(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error", Alert.OK, this);
  }
  if (this.autoRefresh != null) {
    if (!this.autoRefresh.running) {
      this.autoRefresh.start();
    }
  }
}
  // getDataProvider(rows, columnName) {
  //   if (rows) {

  //     if (!rows.length)
  //       rows = [rows];
  //     let dataProvider = [];
  //     rows.forEach(row => {
  //       let content = row[columnName] && row[columnName].content ? row[columnName].content : "";
  //       if (!dataProvider.some(item => item.value === content)) {
  //         dataProvider.push({ value: content, label: content });
  //       }
  //     });
  //     dataProvider.sort((a, b) => a.label.localeCompare(b.label)); // sort alphabetically
  //     return dataProvider;
  //   } else {
  //     return [];
  //   }
  // }

  getDataProvider(rows, columnName) {
    if (rows) {
      if (!rows.length)
        rows = [rows];
      let dataProvider = [];
      rows.forEach(row => {
        let content = row[columnName] && row[columnName].content ? row[columnName].content : "";
        if (!dataProvider.some(item => item.value === content)) {
          if (!content) {
            if (!dataProvider.some(item => item.value === "(EMPTY)")) {
              dataProvider.push({ value: "(EMPTY)", label: "(EMPTY)" });
            }
          } else {
            if (!dataProvider.some(item => item.value === "(NOT EMPTY)")) {
              dataProvider.push({ value: "(NOT EMPTY)", label: "(NOT EMPTY)" });
            }

            dataProvider.push({ value: content, label: content });
          }
        }
        
      });
      dataProvider.sort((a, b) => a.label.localeCompare(b.label)); // sort alphabetically
      return dataProvider;
    } else
      return [];
  }


/**
   * If a fault occurs with the connection with the server then display the lost connection label
   * @param event:FaultEvent
   **/
 inputDataFault(event): void {
    this.invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.error("fault " + this.invalidComms);
 }



 refreshGrid() {
    this.entityId = this.entityCombo.selectedItem.content;
    this.currencyCode = this.ccyCombo.selectedItem.content;
  this.refreshdetails(null);
}

  private lastSelectedFilterCheckboxes: string = "YYNN";
  checkIfAtLeastOneSelected() {
    if (!this.openBox.selected && !this.blockedBox.selected && !this.closedBox.selected &&
      !this.emptyBox.selected) {
      this.restoreLastCheckboxesState();
      return false;
    }
    return true;

  }

  getCurrentCheckboxesState(): string {
    let currentState = "";
    currentState += this.openBox.selected ? "Y" : "N";
    currentState += this.blockedBox.selected ? "Y" : "N";
    currentState += this.closedBox.selected ? "Y" : "N";
    currentState += this.emptyBox.selected ? "Y" : "N";
    return currentState;
  }



  restoreLastCheckboxesState(): void {

    if (this.lastSelectedFilterCheckboxes.length > 0 && this.lastSelectedFilterCheckboxes.length == 4) {
      this.openBox.selected = this.lastSelectedFilterCheckboxes[0] == "Y";
      this.blockedBox.selected = this.lastSelectedFilterCheckboxes[1] == "Y";
      this.closedBox.selected = this.lastSelectedFilterCheckboxes[2] == "Y";
      this.emptyBox.selected = this.lastSelectedFilterCheckboxes[3] == "Y";
    }

  }


  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  endOfComms(): void {
    if (!this.inputData.isBusy()) {
      this.loadingImage.setVisible(false);
    }

  }

  createColumnFilter(obj) {

    const accountIdColumn = obj.columns.column.find(column => column.dataelement === "accountId");
    const accountIdDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "accountId");
    accountIdColumn['FilterType'] = 'MultipleSelect'
    accountIdColumn['dataProvider'] = accountIdDataProvider;
    accountIdColumn['FilterInputSearch'] = true;

    const accountNameColumn = obj.columns.column.find(column => column.dataelement === "accountName");
    accountNameColumn['FilterType'] = "InputSearch";


    const currencyCodeColumn = obj.columns.column.find(column => column.dataelement === "currencyCode");
    const currencyCodeDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "currencyCode");
    currencyCodeColumn['FilterType'] = 'MultipleSelect'
    currencyCodeColumn['dataProvider'] = currencyCodeDataProvider;
    currencyCodeColumn['FilterInputSearch'] = true;

    const corresAccIdColumn = obj.columns.column.find(column => column.dataelement === "corresAccId");
    const corresAccIdDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "corresAccId");
    corresAccIdColumn['FilterType'] = 'MultipleSelect'
    corresAccIdColumn['dataProvider'] = corresAccIdDataProvider;
    corresAccIdColumn['FilterInputSearch'] = true;


    const accountTypeColumn = obj.columns.column.find(column => column.dataelement === "accountType");
    const accountTypeDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "accountType");
    accountTypeDataProvider['FilterType'] = 'MultipleSelect'
    accountTypeDataProvider['dataProvider'] = accountTypeColumn;
    accountTypeDataProvider['FilterInputSearch'] = false;

    const mainAccountIdColumn = obj.columns.column.find(column => column.dataelement === "mainAccountId");
    const mainAccountIdDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "mainAccountId");
    mainAccountIdColumn['FilterType'] = 'MultipleSelect'
    mainAccountIdColumn['dataProvider'] = mainAccountIdDataProvider;
    mainAccountIdColumn['FilterInputSearch'] = true;

    const accountLevelColumn = obj.columns.column.find(column => column.dataelement === "accountLevel");
    const accountLevelDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "accountLevel");
    accountLevelColumn['FilterType'] = 'MultipleSelect'
    accountLevelColumn['dataProvider'] = accountLevelDataProvider;
    accountLevelColumn['FilterInputSearch'] = false;

    const cutOffColumn = obj.columns.column.find(column => column.dataelement === "cutOff");
    const cutOffProvider = this.getDataProvider(this.jsonReader.getGridData().row, "cutOff");
    cutOffColumn['FilterType'] = 'MultipleSelect'
    cutOffColumn['dataProvider'] = cutOffProvider;
    cutOffColumn['FilterInputSearch'] = true;

    const linkAccountIdColumn = obj.columns.column.find(column => column.dataelement === "linkAccountId");
    const linkAccountIdProvider = this.getDataProvider(this.jsonReader.getGridData().row, "linkAccountId");
    linkAccountIdColumn['FilterType'] = 'MultipleSelect'
    linkAccountIdColumn['dataProvider'] = linkAccountIdProvider;
    linkAccountIdColumn['FilterInputSearch'] = true;






    const accountClassColumn = obj.columns.column.find(column => column.dataelement === "accountClass");
    const accountClassDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "accountClass");
    accountClassColumn['FilterType'] = 'MultipleSelect'
    accountClassColumn['dataProvider'] = accountClassDataProvider;
    accountClassColumn['FilterInputSearch'] = false;

    const ibanColumn = obj.columns.column.find(column => column.dataelement === "iban");
    const ibanDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "iban");
    ibanColumn['FilterType'] = 'MultipleSelect'
    ibanColumn['dataProvider'] = ibanDataProvider;
    ibanColumn['FilterInputSearch'] = true;

    const accountPartyIdColumn = obj.columns.column.find(column => column.dataelement === "accountPartyId");
    const accountPartyIdDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "accountPartyId");
    accountPartyIdColumn['FilterType'] = 'MultipleSelect'
    accountPartyIdColumn['dataProvider'] = accountPartyIdDataProvider;
    accountPartyIdColumn['FilterInputSearch'] = true;


    const statusColumn = obj.columns.column.find(column => column.dataelement === "status");
    const statusDataProvider = this.getDataProvider(this.jsonReader.getGridData().row, "status");
    statusColumn['FilterType'] = 'MultipleSelect'
    statusColumn['dataProvider'] = statusDataProvider;
    statusColumn['FilterInputSearch'] = false;

  }

refreshdetails(entityChanged): void {

    this.entityId = this.entityCombo.selectedItem.content;
    this.currencyCode = this.ccyCombo.selectedItem.content;

    this.inputData.cbResult = (event) => {
    this.inputDataResult(event);
  };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "acctMaintenance.do?method=";
  // Then declare the action method:					
    this.actionMethod = "displayListByEntityAngular";

    this.selectedCcy.text = this.ccyCombo.selectedItem.value;
    this.selectedEntity.text = this.entityCombo.selectedItem.value;
    this.requestParams = [];
  this.requestParams["entityId"] = this.entityId;
    if (entityChanged)
    this.requestParams["selectedCurrencyCode"] = "";
  else
    this.requestParams["selectedCurrencyCode"] = this.currencyCode;

    
    const check = this.checkIfAtLeastOneSelected();
    if (!check) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.throuputbreakdown.atleastOneFilter', null), 'Warning');
      return;
}
    let checkedStatus = '';
    checkedStatus += this.openBox.selected ? 'Y' : 'N';
    checkedStatus += this.blockedBox.selected ? 'Y' : 'N';
    checkedStatus += this.closedBox.selected ? 'Y' : 'N';
    checkedStatus += this.emptyBox.selected ? 'Y' : 'N';

    this.requestParams["checkedStatus"] = checkedStatus;

    // this.requestParams["quickFilter"] = this.getQuickFilterContent();
    // this.getQuickFilterContent();

    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);

}
/**
   * doHelp
   *
   * Function is called when "Help" button is click. Displays help window
   */
 doHelp(): void {
  try {
    ExternalInterface.call('help');
  } catch (e) {
    // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'IlmCurrencyCalculation', 'doHelp', this.errorLocation);
  }
}


/**
 * Listener for Error alert message and
 * perform action for when click ok button
 * @param event:CloseEvent
 * */
errorHandler(event): void {
  // check event click is ok button
  if (event.detail == Alert.OK) {
    if (this.closeWindow) {
      this.closeHandler();
        this.closeWindow = false;
    }
  }
}
closeHandler() {
  window.close();
 }
/**
   * doDeleteRule
   *
   * @param event: Event
   *
   * Method to pop up delete confirmation
   *
   */
 doDeleteAccount(): void {
  try {
    Alert.yesLabel = "Yes";
    Alert.noLabel = "No";
    const message = SwtUtil.getPredictMessage('confirm.delete');
    this.swtAlert.confirm(message, 'Alert', Alert.OK | Alert.CANCEL, null, this.deleteAccount.bind(this));
  } catch (e) {
    // log the error in ERROR LOG
    SwtUtil.logError(e, this.moduleId, 'AccountMaintenance', 'doDeleteAccount', this.errorLocation);
  }
}

  /**
   * cellClickEventHandler
   *
   * @param event: Event
   *
   * This method is used to manumberain the button status when a row is clicked
   */
   cellClickEventHandler(event: Event): void {
    try {
      if (this.currencyCalculationGrid.selectedIndex >= 0 && this.currencyCalculationGrid.selectable) {
        this.disableOrEnableButtons(true);
      } else {
        this.disableOrEnableButtons(false);
      }
      event.stopPropagation();
      this.addButton.setFocus();
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'cellClickEventHandler', this.errorLocation);
    }
  }

 /**
   * disableOrEnableButtons()
   *
   * Method called to disable  or enable components
   *
   * @param isRowSelected:boolean
   */

  disableOrEnableButtons(isRowSelected: boolean): void {
    try {
      if (isRowSelected) {
        this.enableChangeButton(this.menuAccessId == 0 && this.menuEntityCurrGrpAccess == 0);
        this.enableViewButton(this.menuAccessId < 2 && this.menuEntityCurrGrpAccess < 2);
        this.enableDeleteButton(this.menuAccessId == 0 && this.menuEntityCurrGrpAccess == 0);
        this.enableOkButton(this.menuAccessId == 0 && this.menuEntityCurrGrpAccess == 0);

        this.accountId = this.currencyCalculationGrid.selectedItem.accountId.content;
        this.accountName = this.currencyCalculationGrid.selectedItem.accountName.content
      } else {
        this.accountId = null;
        this.accountName = null;
        this.enableChangeButton(false);
        this.enableViewButton(false);
        this.enableDeleteButton(false);
        this.enableOkButton(false);

      }
    } catch (e) {
      // log the error in ERROR LOG
      SwtUtil.logError(e, this.moduleId, 'CategoryMaintenance', 'disableOrEnableButtons', this.errorLocation);
    }
  }
  
  /**
   * enableAddButton
   *
   */
   enableAddButton(value): void {
    this.addButton.enabled = value;
    this.addButton.buttonMode = value;
  }
  /**
   * enableChangeButton
   *
   */
  enableChangeButton(value): void {
    this.changeButton.enabled = value;
    this.changeButton.buttonMode = value;
  }
  /**
   * enableViewButton
   *
   */
  enableViewButton(value: boolean): void {
    this.viewButton.enabled = value;
    this.viewButton.buttonMode = value;
  }
  /**
   * enableDeleteButton
   *
   */
  enableDeleteButton(value): void {
    this.deleteButton.enabled = value;
    this.deleteButton.buttonMode = value;
  }
  /**
   * enableDeleteButton
   *
   */
  enableOkButton(value): void {
    this.okbutton.enabled = value;
    this.okbutton.buttonMode = value;
  }


/**
 * deleteRule
 *
 * @param event:
 *
 * Method to remove selected Category Rule
 */
deleteAccount(event): void {
  try {
    // Condition to check Ok Button is selected
    if (event.detail === Alert.OK) {

       // result event
        this.deleteData.cbResult = (event) => {
          this.deleteDataResult(event);
        };


        this.entityId = this.currencyCalculationGrid.selectedItem.entityId.content;
        this.currencyCode = this.currencyCalculationGrid.selectedItem.currencyCode.content;
        this.accountId = this.currencyCalculationGrid.selectedItem.accountId.content;
        this.actionPath = "acctMaintenance.do?method=";
        this.actionMethod = "deleteAccountAngular";
        this.requestParams["selectedEntityId"] = this.entityId;
        this.requestParams["currencyCode"] = this.currencyCode;
        this.requestParams["selectedAccountId"] = this.accountId;
        this.deleteData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.deleteData.send(this.requestParams);
    }
  } catch (e) {
    SwtUtil.logError(e, this.moduleId, 'AccountMaintenance', 'deleteAccount', this.errorLocation);
  }
}


  deleteDataResult(event): void {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {

      // Parse result json
      this.jsonReader.setInputJSON(event);
      if (!this.jsonReader.getRequestReplyStatus()) {
        if ("DataIntegrityViolationExceptioninDelete" == this.jsonReader.getRequestReplyMessage()) {
          this.swtAlert.error(SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninDelete') + SwtUtil.getPredictMessage('alert.ContactSysAdm'), 'Error');
        } else {

          this.swtAlert.error('Error occurred, Please contact your System Administrator: \n'
          + this.jsonReader.getRequestReplyMessage(), 'Error');
        }
      } else {
        this.refreshdetails(null);
      }
    }
  }

  doOpenChildWindow(methodName) {
    if (methodName != 'add') {
      this.entityId = this.currencyCalculationGrid.selectedItem.entityId.content;
    //this.entityCode= this.currencyCalculationGrid.selectedItem.entityId.content;
      this.currencyCode = this.currencyCalculationGrid.selectedItem.currencyCode.content;
    this.accountId = this.currencyCalculationGrid.selectedItem.accountId.content;
      this.accountName = StringUtils.encode64(this.currencyCalculationGrid.selectedItem.accountName.content);
      this.linkedAccount = this.currencyCalculationGrid.selectedItem.linkAccountId.content;
    } else {
    this.entityId = this.entityCombo.selectedLabel;
    this.currencyCode = this.ccyCombo.selectedLabel;
    this.accountId = "";
    this.accountName = "";
      this.linkedAccount = "";
    }
    ExternalInterface.call('openChildWindow', methodName, this.accountId, this.accountName, this.entityId, this.selectedEntity.text, this.currencyCode, this.selectedCcy.text);
  }

  okHandler() {
    window.opener.document.forms[0].elements["ilmCcyParams.primaryAccountId"].value = this.accountId;
    window.opener.document.getElementById("acctName").innerHTML = this.accountName;
    window.opener.document.getElementById("primaryAccountIdSelect").options.length = 0;
   var option = window.opener.document.createElement("option");
     var text = window.opener.document.createTextNode(this.accountId);
   option.appendChild(text)
   option.selected = 'selected';
   option.value = this.accountId;
   window.opener.document.getElementById("primaryAccountIdSelect").appendChild(option);
   self.close();
 }



}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountMaintenance }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountMaintenance],
  entryComponents: []
})
export class AccountMaintenanceModule { }
